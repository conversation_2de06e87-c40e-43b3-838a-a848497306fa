using Marketplaces.Api.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Marketplaces.Api.Requests;

public class CalculateDiscountRequest
{
    [Required(ErrorMessage = $"{nameof(NomenclatureId)} is required")]
    [Range(1, int.MaxValue, ErrorMessage = $"{nameof(NomenclatureId)} must be greater than 0")]
    public int NomenclatureId { get; set; }

    [Required(ErrorMessage = $"{nameof(Marketplace)} is required")]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public Marketplace Marketplace { get; set; }

    [Required(ErrorMessage = $"{nameof(BasePrice)} is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = $"{nameof(BasePrice)} must be greater than 0")]
    public decimal BasePrice { get; set; }

    [Required(ErrorMessage = $"{nameof(PurchasePrice)} is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = $"{nameof(PurchasePrice)} must be greater than 0")]
    public decimal PurchasePrice { get; set; }
}
