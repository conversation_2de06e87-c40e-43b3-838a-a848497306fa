namespace Marketplaces.Api.Databases;

public abstract class MarketplaceNomenclature
{
    public int NomenclatureId { get; set; }

    public decimal? MinimumProfitPercentage { get; set; }

    public decimal? MinimumRevenue { get; set; }

    public string? Size { get; set; }

    public void SetNomenclatureId(int nomenclatureId)
    {
        NomenclatureId = nomenclatureId;
    }

    public void UpdateMinimum(decimal? minimumProfitPercentage, decimal? minimumRevenue)
    {
        MinimumProfitPercentage = minimumProfitPercentage is 0 ? null : minimumProfitPercentage;
        MinimumRevenue = minimumRevenue is 0 ? null : minimumRevenue;
    }

    public void UpdateSize(string? russianSize)
    {
        Size = russianSize;
    }

    public abstract void UpdateTitle(string name);
}